#!/bin/bash

echo "🧪 开始测试Refresh Token机制..."
echo ""

BASE_URL="http://localhost:3000/api"

# 1. 登录获取双Token
echo "1️⃣ 测试登录获取双Token..."
LOGIN_RESPONSE=$(curl -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "phoneNumber": "13800138000",
    "password": "Test123!@#"
  }' \
  -s)

echo "$LOGIN_RESPONSE"
echo ""

# 提取tokens
ACCESS_TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
REFRESH_TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"refresh_token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$ACCESS_TOKEN" ] || [ -z "$REFRESH_TOKEN" ]; then
  echo "❌ 未获取到tokens，测试终止"
  exit 1
fi

echo "🔑 Access Token: ${ACCESS_TOKEN:0:30}..."
echo "🔄 Refresh Token: ${REFRESH_TOKEN:0:30}..."
echo ""

# 2. 使用Access Token访问受保护资源
echo "2️⃣ 使用Access Token访问受保护资源..."
curl -X GET "$BASE_URL/user" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -w "\n状态码: %{http_code}\n" \
  -s

echo ""

# 3. 使用Refresh Token获取新的Access Token
echo "3️⃣ 使用Refresh Token获取新的Access Token..."
REFRESH_RESPONSE=$(curl -X POST "$BASE_URL/auth/refresh" \
  -H "Content-Type: application/json" \
  -d "{\"refresh_token\": \"$REFRESH_TOKEN\"}" \
  -w "\n状态码: %{http_code}" \
  -s)

echo "$REFRESH_RESPONSE"
echo ""

# 提取新的Access Token
NEW_ACCESS_TOKEN=$(echo "$REFRESH_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)

if [ -n "$NEW_ACCESS_TOKEN" ]; then
  echo "🆕 新的Access Token: ${NEW_ACCESS_TOKEN:0:30}..."
  echo ""
  
  # 4. 使用新的Access Token访问资源
  echo "4️⃣ 使用新的Access Token访问资源..."
  curl -X GET "$BASE_URL/user" \
    -H "Authorization: Bearer $NEW_ACCESS_TOKEN" \
    -w "\n状态码: %{http_code}\n" \
    -s
  echo ""
fi

# 5. 测试登出功能
echo "5️⃣ 测试登出功能..."
curl -X POST "$BASE_URL/auth/logout" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $NEW_ACCESS_TOKEN" \
  -d "{\"refresh_token\": \"$REFRESH_TOKEN\"}" \
  -w "\n状态码: %{http_code}\n" \
  -s

echo ""

# 6. 验证Refresh Token是否已失效
echo "6️⃣ 验证Refresh Token是否已失效..."
curl -X POST "$BASE_URL/auth/refresh" \
  -H "Content-Type: application/json" \
  -d "{\"refresh_token\": \"$REFRESH_TOKEN\"}" \
  -w "\n状态码: %{http_code}\n" \
  -s

echo ""
echo "🎉 Refresh Token机制测试完成！"
