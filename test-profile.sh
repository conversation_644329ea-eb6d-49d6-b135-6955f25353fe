#!/bin/bash

echo "🧪 测试获取当前用户信息接口..."
echo ""

BASE_URL="http://localhost:3000/api"

# 1. 登录获取Token
echo "1️⃣ 登录获取Access Token..."
LOGIN_RESPONSE=$(curl -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "phoneNumber": "13800138000",
    "password": "Test123!@#"
  }' \
  -s)

echo "$LOGIN_RESPONSE"
echo ""

# 提取Access Token
ACCESS_TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$ACCESS_TOKEN" ]; then
  echo "❌ 未获取到Access Token，测试终止"
  exit 1
fi

echo "🔑 Access Token: ${ACCESS_TOKEN:0:30}..."
echo ""

# 2. 测试 /api/auth/profile 接口
echo "2️⃣ 测试 /api/auth/profile 接口..."
curl -X GET "$BASE_URL/auth/profile" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -w "\n状态码: %{http_code}\n" \
  -s

echo ""

# 3. 测试 /api/auth/me 接口
echo "3️⃣ 测试 /api/auth/me 接口..."
curl -X GET "$BASE_URL/auth/me" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -w "\n状态码: %{http_code}\n" \
  -s

echo ""

# 4. 测试无Token访问（应该返回401）
echo "4️⃣ 测试无Token访问（应该返回401）..."
curl -X GET "$BASE_URL/auth/profile" \
  -w "\n状态码: %{http_code}\n" \
  -s

echo ""

# 5. 测试无效Token访问（应该返回401）
echo "5️⃣ 测试无效Token访问（应该返回401）..."
curl -X GET "$BASE_URL/auth/profile" \
  -H "Authorization: Bearer invalid_token" \
  -w "\n状态码: %{http_code}\n" \
  -s

echo ""
echo "🎉 用户信息接口测试完成！"
