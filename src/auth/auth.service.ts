import { Injectable } from '@nestjs/common';
import { RegisterDto } from './dto/RegisterDto.dto';
import { LoginDto } from './dto/LoginDto.dto';
import { UserService } from 'src/user/user.service';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import {
  BusinessException,
  ResourceConflictException,
  AuthException
} from '../common/exceptions/business.exception';

@Injectable()
export class AuthService {
  constructor(private readonly userService: UserService, private readonly jwtService: JwtService) {}

  /**
   * 验证用户凭据（用于本地认证策略）
   * @param phoneNumber 手机号
   * @param password 密码
   * @returns 验证成功返回用户信息，失败返回null
   */
  async validateUser(phoneNumber: string, password: string): Promise<any> {
    const user = await this.userService.findOneByPhoneNumber(phoneNumber);
    if (user && await bcrypt.compare(password, user.password)) {
      // 移除密码字段，返回安全的用户信息
      const { password, ...result } = user;
      return result;
    }
    return null;
  }

  async register(registerDto: RegisterDto) {
    if(registerDto.password !== registerDto.confirmPassword){
      throw new BusinessException('两次输入的密码不一致');
    }
    if(await this.userService.findOneByPhoneNumber(registerDto.phoneNumber)){
      throw new ResourceConflictException('该手机号已被注册');
    }
    // 校验验证码
    if(registerDto.code !== '123456'){
      throw new BusinessException('验证码错误');
    }
    // hash加密
    const hashedPassword = await bcrypt.hash(registerDto.password, 10);
    registerDto.password = hashedPassword;
    await this.userService.create(registerDto);
    return {
      message: '注册成功',
    }
  }

  /**
   * 生成JWT token（用于登录成功后）
   * @param user 用户信息
   * @returns 包含token的响应
   */
  async login(user: any) {
    const payload = {
      sub: user.id,
      username: user.username
    };

    return {
      message: '登录成功',
      access_token: this.jwtService.sign(payload),
    };
  }
}
