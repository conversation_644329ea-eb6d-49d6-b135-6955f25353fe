declare module 'passport-jwt' {
  import { Request } from 'express';

  export interface ExtractJwt {
    fromAuthHeaderAsBearerToken(): (request: Request) => string | null;
    fromHeader(header: string): (request: Request) => string | null;
    fromBodyField(field: string): (request: Request) => string | null;
    fromUrlQueryParameter(param: string): (request: Request) => string | null;
    fromExtractors(extractors: Array<(request: Request) => string | null>): (request: Request) => string | null;
  }

  export const ExtractJwt: ExtractJwt;

  export interface StrategyOptions {
    jwtFromRequest: (request: Request) => string | null;
    secretOrKey: string | Buffer;
    issuer?: string;
    audience?: string;
    algorithms?: string[];
    ignoreExpiration?: boolean;
    passReqToCallback?: boolean;
    jsonWebTokenOptions?: any;
  }

  export interface VerifyCallback {
    (error: any, user?: any, info?: any): void;
  }

  export interface VerifyCallbackWithRequest {
    (req: Request, payload: any, done: VerifyCallback): void;
  }

  export interface VerifyCallbackWithoutRequest {
    (payload: any, done: VerifyCallback): void;
  }

  export class Strategy {
    constructor(options: StrategyOptions, verify: VerifyCallbackWithRequest | VerifyCallbackWithoutRequest);
    authenticate(req: Request, options?: any): void;
  }
}
