#!/bin/bash

echo "🧪 测试CORS配置..."
echo ""

BASE_URL="http://localhost:3000/api"

# 1. 测试OPTIONS预检请求（模拟浏览器行为）
echo "1️⃣ 测试OPTIONS预检请求..."
curl -X OPTIONS "$BASE_URL/auth/login" \
  -H "Origin: http://localhost:3001" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type,Authorization" \
  -v \
  -s

echo ""
echo ""

# 2. 测试实际的跨域POST请求
echo "2️⃣ 测试跨域POST请求（模拟前端登录）..."
curl -X POST "$BASE_URL/auth/login" \
  -H "Origin: http://localhost:3001" \
  -H "Content-Type: application/json" \
  -d '{
    "phoneNumber": "13800138000",
    "password": "Test123!@#"
  }' \
  -v \
  -s

echo ""
echo ""

# 3. 测试跨域GET请求
echo "3️⃣ 测试跨域GET请求..."
curl -X GET "$BASE_URL" \
  -H "Origin: http://localhost:3001" \
  -v \
  -s

echo ""
echo ""

# 4. 测试不允许的域名（应该被拒绝）
echo "4️⃣ 测试不允许的域名..."
curl -X GET "$BASE_URL" \
  -H "Origin: http://localhost:4000" \
  -v \
  -s

echo ""
echo "🎉 CORS测试完成！"
