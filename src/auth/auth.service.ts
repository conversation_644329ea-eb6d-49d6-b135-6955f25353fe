import { Injectable } from '@nestjs/common';
import { RegisterDto } from './dto/RegisterDto.dto';
import { LoginDto } from './dto/LoginDto.dto';
import { UserService } from 'src/user/user.service';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import {
  BusinessException,
  ResourceConflictException,
  AuthException
} from '../common/exceptions/business.exception';

@Injectable()
export class AuthService {
  constructor(private readonly userService: UserService, private readonly jwtService: JwtService) {}

  async register(registerDto: RegisterDto) {
    if(registerDto.password !== registerDto.confirmPassword){
      throw new BusinessException('两次输入的密码不一致');
    }
    if(await this.userService.findOneByPhoneNumber(registerDto.phoneNumber)){
      throw new ResourceConflictException('该手机号已被注册');
    }
    // 校验验证码
    if(registerDto.code !== '123456'){
      throw new BusinessException('验证码错误');
    }
    // hash加密
    const hashedPassword = await bcrypt.hash(registerDto.password, 10);
    registerDto.password = hashedPassword;
    await this.userService.create(registerDto);
    return {
      message: '注册成功',
    }
  }

  async login(loginDto: LoginDto) {
    const user = await this.userService.findOneByPhoneNumber(loginDto.phoneNumber);
    if(!user){
      throw new AuthException('用户名或密码错误');
    }

    const isMatch = await bcrypt.compare(loginDto.password, user.password);
    if(!isMatch){
      throw new AuthException('用户名或密码错误');
    }

    const token = this.jwtService.sign({
      sub: user.id,
      username: user.username
    });
    return {
      message: '登录成功',
      access_token: token,
    }
  }
}
