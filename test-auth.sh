#!/bin/bash

echo "🧪 开始测试Passport JWT认证..."
echo ""

BASE_URL="http://localhost:3000/api"

# 1. 测试注册
echo "1️⃣ 测试用户注册..."
curl -X POST "$BASE_URL/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "phoneNumber": "13800138000",
    "code": "123456",
    "password": "Test123!@#",
    "confirmPassword": "Test123!@#"
  }' \
  -w "\n状态码: %{http_code}\n" \
  -s

echo ""

# 2. 测试登录
echo "2️⃣ 测试用户登录..."
LOGIN_RESPONSE=$(curl -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "phoneNumber": "13800138000",
    "password": "Test123!@#"
  }' \
  -w "\n状态码: %{http_code}" \
  -s)

echo "$LOGIN_RESPONSE"
echo ""

# 提取token（简单的方法，实际项目中应该用更好的JSON解析）
TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
  echo "❌ 未获取到token，测试终止"
  exit 1
fi

echo "🔑 获取到token: ${TOKEN:0:20}..."
echo ""

# 3. 测试访问受保护的路由（不带token）
echo "3️⃣ 测试访问受保护路由（不带token）..."
curl -X GET "$BASE_URL/user" \
  -w "\n状态码: %{http_code}\n" \
  -s

echo ""

# 4. 测试访问受保护的路由（带token）
echo "4️⃣ 测试访问受保护路由（带token）..."
curl -X GET "$BASE_URL/user" \
  -H "Authorization: Bearer $TOKEN" \
  -w "\n状态码: %{http_code}\n" \
  -s

echo ""

# 5. 测试访问公共路由
echo "5️⃣ 测试访问公共路由..."
curl -X GET "$BASE_URL" \
  -w "\n状态码: %{http_code}\n" \
  -s

echo ""
echo "🎉 Passport JWT测试完成！"
