#!/bin/bash

echo "🎯 最终CORS测试 - 模拟浏览器请求"
echo ""

BASE_URL="http://localhost:3000/api"

# 1. 测试OPTIONS预检请求
echo "1️⃣ 测试OPTIONS预检请求..."
curl -X OPTIONS "$BASE_URL/auth/login" \
  -H "Origin: http://localhost:3001" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type,Authorization,sec-ch-ua,sec-ch-ua-platform,Platform" \
  -w "\n状态码: %{http_code}\n" \
  -s

echo ""

# 2. 测试实际的POST请求（模拟浏览器）
echo "2️⃣ 测试实际POST请求（模拟Chrome浏览器）..."
curl -X POST "$BASE_URL/auth/login" \
  -H "Origin: http://localhost:3001" \
  -H "Referer: http://localhost:3001/" \
  -H "User-Agent: Mozilla/5.0 (<PERSON>; Intel Mac OS X 10_15_7) AppleWebKit/537.36" \
  -H "sec-ch-ua-platform: \"macOS\"" \
  -H "sec-ch-ua: \"Chromium\";v=\"134\"" \
  -H "sec-ch-ua-mobile: ?0" \
  -H "Content-Type: application/json" \
  -H "Platform: 1" \
  -d '{"phoneNumber":"13800138000","password":"Test123!@#"}' \
  -w "\n状态码: %{http_code}\n" \
  -s

echo ""

# 3. 测试GET请求
echo "3️⃣ 测试GET请求..."
curl -X GET "$BASE_URL" \
  -H "Origin: http://localhost:3001" \
  -H "Referer: http://localhost:3001/" \
  -w "\n状态码: %{http_code}\n" \
  -s

echo ""
echo "✅ 如果以上请求都返回正确的状态码（OPTIONS: 204, POST: 200, GET: 200），"
echo "   那么CORS配置就是正确的，你的前端应该可以正常访问后端API了！"
